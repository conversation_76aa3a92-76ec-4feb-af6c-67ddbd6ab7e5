/**
 * 更新现有用户密码为加密形式的脚本
 * 运行方式: node scripts/update-passwords.js
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const passwordUtil = require('../utils/password');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/blog', function(err) {
    if (err) {
        console.log('数据库连接失败');
        return;
    }
    
    console.log('数据库连接成功，开始更新密码...');
    
    // 查找所有用户
    User.find().then(function(users) {
        console.log(`找到 ${users.length} 个用户需要更新密码`);
        
        // 记录更新成功的用户数量
        let successCount = 0;
        
        // 处理每个用户
        const promises = users.map(function(user) {
            // 检查密码是否已经加密（bcrypt加密的密码通常以$2a$、$2b$或$2y$开头）
            if (user.password && !user.password.startsWith('$2')) {
                // 密码未加密，进行加密
                const hashedPassword = passwordUtil.encrypt(user.password);
                user.password = hashedPassword;
                
                return user.save().then(function() {
                    successCount++;
                    console.log(`用户 ${user.username} 密码更新成功`);
                }).catch(function(err) {
                    console.error(`用户 ${user.username} 密码更新失败:`, err);
                });
            } else {
                console.log(`用户 ${user.username} 密码已经加密，跳过`);
                return Promise.resolve();
            }
        });
        
        // 等待所有更新完成
        Promise.all(promises).then(function() {
            console.log(`密码更新完成，成功更新 ${successCount} 个用户的密码`);
            mongoose.disconnect();
            process.exit(0);
        }).catch(function(err) {
            console.error('更新过程中出错:', err);
            mongoose.disconnect();
            process.exit(1);
        });
    }).catch(function(err) {
        console.error('查询用户失败:', err);
        mongoose.disconnect();
        process.exit(1);
    });
}); 