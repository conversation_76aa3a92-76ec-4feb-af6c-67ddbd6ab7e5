/**
 * 管理后台前端脚本
 */

$(function() {
    // 获取JWT令牌
    function getAdminToken() {
        return localStorage.getItem('adminToken');
    }
    
    // 添加JWT令牌到Ajax请求头
    $.ajaxSetup({
        beforeSend: function(xhr) {
            const token = getAdminToken();
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        }
    });
    
    // 检查JWT令牌是否存在，如果不存在且不在登录页面，则重定向到登录页面
    function checkAuth() {
        const token = getAdminToken();
        const isLoginPage = window.location.pathname.includes('/admin/login');
        
        if (!token && !isLoginPage) {
            window.location.href = '/admin/login';
            return false;
        }
        
        return true;
    }
    
    // 页面加载时检查认证状态
    checkAuth();
    
    // 管理员退出
    $('#adminLogout').on('click', function(e) {
        e.preventDefault();
        
        // 清除普通用户cookie
        $.ajax({
            type: 'get',
            url: '/api/user/logout',
            success: function() {
                // 清除管理员cookie
                $.ajax({
                    type: 'get',
                    url: '/api/admin/logout',
                    success: function(result) {
                        // 清除本地存储的令牌
                        localStorage.removeItem('adminToken');
                        // 重定向到登录页面
                        window.location.href = '/admin/login';
                    }
                });
            }
        });
    });
    
    // 处理未授权错误
    $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
        if (jqXHR.status === 401 || jqXHR.status === 403) {
            // 清除本地存储的令牌
            localStorage.removeItem('adminToken');
            // 重定向到登录页面并显示错误信息
            window.location.href = '/admin/login?message=' + encodeURIComponent('您的登录已过期或无效，请重新登录');
        }
    });
}); 