<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <link href="/public/css/bootstrap.min.css" rel="stylesheet">
    <link href="/public/css/bootstrap-theme.min.css" rel="stylesheet">
    <link href="/public/fontAwesome/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f8f8;
            padding-top: 40px;
            padding-bottom: 40px;
        }
        .login-panel {
            max-width: 400px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header img {
            width: 80px;
        }
        .login-header h2 {
            margin-top: 15px;
            color: #333;
        }
        .btn-login {
            background-color: #337ab7;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-panel">
            <div class="login-header">
                <img src="/public/images/00002637.png" alt="博客Logo">
                <h2>管理员登录</h2>
            </div>
            
            {% if message %}
            <div class="alert alert-danger">{{ message }}</div>
            {% endif %}
            
            <form id="adminLoginForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" placeholder="请输入管理员用户名">
                    </div>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码">
                    </div>
                </div>
                <div class="form-group">
                    <p class="text-danger" id="errorMessage"></p>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-login btn-block" id="loginBtn">登录</button>
                </div>
                <div class="text-center">
                    <a href="/" class="text-muted">返回网站首页</a>
                </div>
            </form>
        </div>
    </div>

    <script src="/public/js/jquery-1.12.4.min.js"></script>
    <script src="/public/js/bootstrap.min.js"></script>
    <script>
        $(function() {
            // 确保清除普通用户cookie
            $.ajax({
                url: '/api/user/logout'
            });

            // 管理员登录
            $('#loginBtn').on('click', function() {
                var username = $('#username').val();
                var password = $('#password').val();
                
                if (!username || !password) {
                    $('#errorMessage').text('用户名和密码不能为空');
                    return;
                }
                
                // 发送登录请求
                $.ajax({
                    type: 'post',
                    url: '/api/admin/login',
                    data: {
                        username: username,
                        password: password
                    },
                    dataType: 'json',
                    success: function(result) {
                        if (result.code) {
                            $('#errorMessage').text(result.message);
                        } else {
                            // 登录成功，保存令牌并跳转
                            localStorage.setItem('adminToken', result.token);
                            window.location.href = '/admin';
                        }
                    },
                    error: function() {
                        $('#errorMessage').text('登录请求失败，请稍后重试');
                    }
                });
            });
            
            // 按Enter键提交表单
            $('#password').on('keypress', function(e) {
                if (e.which === 13) {
                    $('#loginBtn').click();
                }
            });
        });
    </script>
</body>
</html> 