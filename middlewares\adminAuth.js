/**
 * 管理员JWT认证中间件
 */
const User = require('../models/User');
const jwtUtil = require('../utils/jwt');

/**
 * 验证管理员JWT令牌
 * 如果验证成功，将管理员信息附加到req.adminInfo
 */
module.exports = async (req, res, next) => {
    // 从请求中提取令牌
    const token = jwtUtil.extractTokenFromRequest(req);
    
    if (!token) {
        // 如果是API请求，返回JSON响应
        if (req.path.startsWith('/api/')) {
            return res.status(401).json({
                code: 401,
                message: '未提供认证令牌，请先登录'
            });
        }
        // 否则重定向到登录页面
        return res.redirect('/admin/login?message=' + encodeURIComponent('请先登录'));
    }
    
    // 验证令牌
    const decoded = jwtUtil.verifyToken(token);
    
    if (!decoded) {
        // 如果是API请求，返回JSON响应
        if (req.path.startsWith('/api/')) {
            return res.status(401).json({
                code: 401,
                message: '认证令牌无效或已过期，请重新登录'
            });
        }
        // 否则重定向到登录页面
        return res.redirect('/admin/login?message=' + encodeURIComponent('认证已过期，请重新登录'));
    }
    
    try {
        // 验证用户是否存在且是管理员
        const admin = await User.findById(decoded._id);
        
        if (!admin || !admin.isAdmin) {
            // 如果是API请求，返回JSON响应
            if (req.path.startsWith('/api/')) {
                return res.status(403).json({
                    code: 403,
                    message: '您没有管理员权限'
                });
            }
            // 否则重定向到登录页面
            return res.redirect('/admin/login?message=' + encodeURIComponent('您没有管理员权限'));
        }
        
        // 将管理员信息附加到req对象
        req.adminInfo = {
            _id: admin._id,
            username: admin.username,
            isAdmin: admin.isAdmin
        };
        
        next();
    } catch (error) {
        console.error('管理员认证中间件错误:', error);
        // 如果是API请求，返回JSON响应
        if (req.path.startsWith('/api/')) {
            return res.status(500).json({
                code: 500,
                message: '服务器内部错误'
            });
        }
        // 否则重定向到错误页面
        return res.redirect('/admin/login?message=' + encodeURIComponent('服务器内部错误'));
    }
}; 