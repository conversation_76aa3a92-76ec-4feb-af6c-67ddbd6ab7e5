/**
 * JWT工具模块
 */
const jwt = require('jsonwebtoken');

// JWT密钥，应存储在环境变量中，这里为示例简化处理
const JWT_SECRET = 'your-super-secret-jwt-key-for-admin-auth';

/**
 * 生成JWT令牌
 * @param {Object} payload - 要编码到令牌中的数据
 * @param {string} expiresIn - 令牌有效期，默认为'1d'（一天）
 * @returns {string} - JWT令牌
 */
exports.generateToken = (payload, expiresIn = '1d') => {
    return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

/**
 * 验证JWT令牌
 * @param {string} token - 要验证的JWT令牌
 * @returns {Object|null} - 如果验证成功，返回解码后的payload；如果验证失败，返回null
 */
exports.verifyToken = (token) => {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (error) {
        console.error('JWT验证失败:', error.message);
        return null;
    }
};

/**
 * 从请求中提取JWT令牌
 * @param {Object} req - Express请求对象
 * @returns {string|null} - 如果找到令牌，则返回令牌；否则返回null
 */
exports.extractTokenFromRequest = (req) => {
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        return req.headers.authorization.substring(7);
    } else if (req.cookies && req.cookies.get('adminToken')) {
        return req.cookies.get('adminToken');
    }
    return null;
}; 