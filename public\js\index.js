/**
 * Created by 毅 on 2016/8/28.
 */

$(function() {

    var $loginBox = $('#loginBox');
    var $registerBox = $('#registerBox');
    var $userInfo = $('#userInfo');
    var $forgotPasswordBox = $('#forgotPasswordBox');

    //切换到注册面板
    $loginBox.find('a.colMint').on('click', function() {
        if ($(this).attr('id') === 'showForgotPassword') {
            $forgotPasswordBox.show();
            $loginBox.hide();
        } else {
            $registerBox.show();
            $loginBox.hide();
        }
    });

    //切换到登录面板
    $registerBox.find('a.colMint').on('click', function() {
        $loginBox.show();
        $registerBox.hide();
    });
    
    //从忘记密码返回登录
    $forgotPasswordBox.find('a.colMint').on('click', function() {
        $loginBox.show();
        $forgotPasswordBox.hide();
    });

    //注册
    $registerBox.find('button').on('click', function() {
        //通过ajax提交请求
        $.ajax({
            type: 'post',
            url: '/api/user/register',
            data: {
                username: $registerBox.find('[name="username"]').val(),
                email: $registerBox.find('[name="email"]').val(),
                password: $registerBox.find('[name="password"]').val(),
                repassword: $registerBox.find('[name="repassword"]').val()
            },
            dataType: 'json',
            success: function(result) {
                $registerBox.find('.colWarning').html(result.message);

                if (!result.code) {
                    //注册成功
                    setTimeout(function() {
                        $loginBox.show();
                        $registerBox.hide();
                    }, 1000);
                }

            }
        });
    });

    //登录
    $loginBox.find('button').on('click', function() {
        //通过ajax提交请求
        $.ajax({
            type: 'post',
            url: '/api/user/login',
            data: {
                username: $loginBox.find('[name="username"]').val(),
                password: $loginBox.find('[name="password"]').val()
            },
            dataType: 'json',
            success: function(result) {

                $loginBox.find('.colWarning').html(result.message);

                if (!result.code) {
                    //登录成功
                    window.location.reload();
                }
            }
        })
    });
    
    //忘记密码
    $forgotPasswordBox.find('button').on('click', function() {
        var email = $forgotPasswordBox.find('[name="email"]').val();
        
        if (!email) {
            $forgotPasswordBox.find('.colWarning').html('请输入邮箱地址');
            return;
        }
        
        //通过ajax提交请求
        $.ajax({
            type: 'post',
            url: '/api/user/forgot-password',
            data: {
                email: email
            },
            dataType: 'json',
            success: function(result) {
                $forgotPasswordBox.find('.colWarning').html(result.message);

                if (!result.code) {
                    //发送成功
                    setTimeout(function() {
                        $loginBox.show();
                        $forgotPasswordBox.hide();
                    }, 3000);
                }
            }
        });
    });

    //退出
    $('#logout').on('click', function() {
        $.ajax({
            url: '/api/user/logout',
            success: function(result) {
                if (!result.code) {
                    window.location.reload();
                }
            }
        });
    });
    
    // 登录模态框提交按钮
    $('#loginSubmit').on('click', function() {
        $.ajax({
            type: 'post',
            url: '/api/user/login',
            data: {
                username: $('#loginUsername').val(),
                password: $('#loginPassword').val()
            },
            dataType: 'json',
            success: function(result) {
                if (result.code) {
                    $('#loginError').text(result.message);
                } else {
                    // 登录成功
                    $('#loginModal').modal('hide');
                    window.location.reload();
                }
            }
        });
    });
    
    // 注册模态框提交按钮
    $('#registerSubmit').on('click', function() {
        var username = $('#registerUsername').val();
        var email = $('#registerEmail').val();
        var password = $('#registerPassword').val();
        var repassword = $('#registerRepassword').val();
        
        $.ajax({
            type: 'post',
            url: '/api/user/register',
            data: {
                username: username,
                email: email,
                password: password,
                repassword: repassword
            },
            dataType: 'json',
            success: function(result) {
                if (result.code) {
                    $('#registerError').text(result.message);
                } else {
                    // 注册成功
                    $('#registerModal').modal('hide');
                    $('#loginModal').modal('show');
                    $('#loginError').text('注册成功，请登录');
                }
            }
        });
    });

});