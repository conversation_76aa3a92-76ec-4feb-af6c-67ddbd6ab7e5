/**
 * 邮件发送工具
 */
const nodemailer = require("nodemailer");
const config = {
    host: "smtp.qq.com",
    port: 465,
    secure: true,
    auth: {
        user: "<EMAIL>",
        pass: "yyvypqxasxelbfja",
    },
    from: '"博客系统" <<EMAIL>>'
};

// 创建邮件传输器
const transporter = nodemailer.createTransport(config);

/**
 * 发送文本邮件
 * @param {string} to - 收件人邮箱
 * @param {string} subject - 邮件主题
 * @param {string} text - 邮件内容（纯文本）
 * @returns {Promise} - 发送结果
 */
exports.sendTextMail = async (to, subject, text) => {
    return await transporter.sendMail({
        from: config.from,
        to: to,
        subject: subject,
        text: text
    });
};

/**
 * 发送HTML邮件
 * @param {string} to - 收件人邮箱
 * @param {string} subject - 邮件主题
 * @param {string} html - 邮件内容（HTML格式）
 * @returns {Promise} - 发送结果
 */
exports.sendHtmlMail = async (to, subject, html) => {
    return await transporter.sendMail({
        from: config.from,
        to: to,
        subject: subject,
        html: html
    });
};

/**
 * 发送验证邮件
 * @param {string} to - 收件人邮箱
 * @param {string} username - 用户名
 * @param {string} token - 验证令牌
 * @returns {Promise} - 发送结果
 */
exports.sendVerificationMail = async (to, username, token) => {
    const verificationLink = `http://localhost:3000/api/user/verify?email=${to}&token=${token}`;
    const html = `
        <div style="background-color: #f7f7f7; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 5px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <h2 style="color: #333; text-align: center;">邮箱验证</h2>
                <p>您好，<strong>${username}</strong>！</p>
                <p>感谢您注册我们的博客系统。请点击下面的链接验证您的邮箱地址：</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${verificationLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">验证邮箱</a>
                </div>
                <p>或者，您可以复制以下链接到浏览器地址栏：</p>
                <p style="word-break: break-all;">${verificationLink}</p>
                <p>此链接将在24小时后失效。</p>
                <p>如果您没有注册账号，请忽略此邮件。</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #777; font-size: 12px; text-align: center;">此邮件由系统自动发送，请勿回复。</p>
            </div>
        </div>
    `;
    
    return await transporter.sendMail({
        from: config.from,
        to: to,
        subject: '请验证您的邮箱',
        html: html
    });
};

/**
 * 发送密码重置邮件
 * @param {string} to - 收件人邮箱
 * @param {string} username - 用户名
 * @param {string} token - 重置令牌
 * @returns {Promise} - 发送结果
 */
exports.sendPasswordResetMail = async (to, username, token) => {
    const resetLink = `http://localhost:3000/reset-password?email=${to}&token=${token}`;
    const html = `
        <div style="background-color: #f7f7f7; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 5px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <h2 style="color: #333; text-align: center;">密码重置</h2>
                <p>您好，<strong>${username}</strong>！</p>
                <p>我们收到了您重置密码的请求。请点击下面的链接重置您的密码：</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${resetLink}" style="background-color: #2196F3; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">重置密码</a>
                </div>
                <p>或者，您可以复制以下链接到浏览器地址栏：</p>
                <p style="word-break: break-all;">${resetLink}</p>
                <p>此链接将在1小时后失效。</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #777; font-size: 12px; text-align: center;">此邮件由系统自动发送，请勿回复。</p>
            </div>
        </div>
    `;
    
    return await transporter.sendMail({
        from: config.from,
        to: to,
        subject: '重置您的密码',
        html: html
    });
};

/**
 * 发送通知邮件
 * @param {string} to - 收件人邮箱
 * @param {string} subject - 邮件主题
 * @param {string} message - 通知消息
 * @returns {Promise} - 发送结果
 */
exports.sendNotificationMail = async (to, subject, message) => {
    const html = `
        <div style="background-color: #f7f7f7; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 5px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <h2 style="color: #333; text-align: center;">${subject}</h2>
                <div style="padding: 20px 0;">
                    ${message}
                </div>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #777; font-size: 12px; text-align: center;">此邮件由系统自动发送，请勿回复。</p>
            </div>
        </div>
    `;
    
    return await transporter.sendMail({
        from: config.from,
        to: to,
        subject: subject,
        html: html
    });
};

// 导出邮件传输器，以便直接使用
exports.transporter = transporter;

