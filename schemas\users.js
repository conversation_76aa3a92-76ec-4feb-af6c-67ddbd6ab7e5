/**
 * Created by 毅 on 2016/8/28.
 */

var mongoose = require('mongoose');

//用户的表结构
module.exports = new mongoose.Schema({

    //用户名
    username: String,
    //密码
    password: String,
    //邮箱
    email: {
        type: String,
        default: ''
    },
    //邮箱是否已验证
    isVerified: {
        type: Boolean,
        default: false
    },
    //验证令牌
    verificationToken: {
        type: String,
        default: ''
    },
    //验证令牌过期时间
    verificationExpires: {
        type: Date,
        default: null
    },
    //密码重置令牌
    resetPasswordToken: {
        type: String,
        default: ''
    },
    //密码重置令牌过期时间
    resetPasswordExpires: {
        type: Date,
        default: null
    },
    //是否是管理员
    isAdmin: {
        type: Boolean,
        default: false
    },
    //注册时间
    registerTime: {
        type: Date,
        default: Date.now
    }

});