{% extends 'layout.html' %}

{% block main %}
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><a href="/admin">管理首页</a></li>
        <li class="active">文件列表</li>
    </ol>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">批量文件上传</h3>
        </div>
        <div class="panel-body">
            <form id="batchUploadForm" class="form-horizontal" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="batchFiles" class="col-sm-2 control-label">选择文件</label>
                    <div class="col-sm-6">
                        <input type="file" id="batchFiles" name="files" class="form-control" accept="image/*" multiple aria-label="选择多个图片文件" title="选择要上传的多个图片">
                        <p class="help-block">最多可同时上传5个文件</p>
                    </div>
                    <div class="col-sm-4">
                        <button type="submit" class="btn btn-primary">上传</button>
                    </div>
                </div>
            </form>
            <div id="uploadProgress" class="progress" style="display: none;">
                <div class="progress-bar progress-bar-striped active" role="progressbar" style="width: 0%">
                    <span class="sr-only">上传进度</span>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">已上传文件列表</h3>
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>预览</th>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="fileList">
                        <!-- 文件列表将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// 处理文件上传
document.getElementById('batchUploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const progressBar = document.querySelector('#uploadProgress .progress-bar');
    
    // 显示进度条
    document.getElementById('uploadProgress').style.display = 'block';
    progressBar.style.width = '0%';
    
    fetch('/admin/upload/multiple', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 上传成功，更新文件列表
            loadFileList();
            // 重置表单
            this.reset();
            alert('文件上传成功！');
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('上传失败，请重试');
    })
    .finally(() => {
        // 隐藏进度条
        document.getElementById('uploadProgress').style.display = 'none';
    });
});

// 加载文件列表
function loadFileList() {
    fetch('/admin/api/files')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = data.files.map(file => `
                <tr>
                    <td>
                        <img src="${file.thumbnailPath}" alt="${file.originalName}" style="height: 50px;">
                    </td>
                    <td>${file.originalName}</td>
                    <td>${formatFileSize(file.size)}</td>
                    <td>${new Date(file.uploadTime).toLocaleString()}</td>
                    <td>
                        <div class="btn-group">
                            <a href="${file.processedPath}" class="btn btn-xs btn-info" target="_blank">查看</a>
                            <button class="btn btn-xs btn-danger" onclick="deleteFile('${file._id}')">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('加载文件列表失败');
    });
}

// 删除文件
function deleteFile(fileId) {
    if (confirm('确定要删除这个文件吗？')) {
        fetch(`/admin/api/files/${fileId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadFileList();
                alert('文件删除成功！');
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        });
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面加载完成后加载文件列表
document.addEventListener('DOMContentLoaded', loadFileList);
</script>
{% endblock %}