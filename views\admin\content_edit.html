{% extends 'layout.html' %}

{% block main %}

<ol class="breadcrumb">
    <li><a href="/">管理首页</a></li>
    <li><span>内容修改</span></li>
</ol>

<h3>内容修改 - {{content.title}}</h3>

<form role="form" method="post">
    <div class="form-group">
        <label for="title">分类：</label>
        <select name="category" id="category" class="form-control">
            {%for category in categories%}
                {%if content.category._id.toString() == category._id.toString()%}
                    <option value="{{category.id}}" selected>{{category.name}}</option>
                {%else%}
                    <option value="{{category.id}}">{{category.name}}</option>
                {%endif%}
            {%endfor%}
        </select>
    </div>

    <div class="form-group">
        <label for="title">标题：</label>
        <input type="text" value="{{content.title}}" class="form-control" id="title" placeholder="请输入内容标题" name="title">
    </div>

    <div class="form-group">
        <label for="description">简介：</label>
        <textarea name="description" id="description" class="form-control" rows="5" placeholder="请输入内容简介">{{content.description}}</textarea>
    </div>

    <div class="form-group">
        <label for="content">内容：</label>
        <textarea name="content" id="content" class="form-control" rows="10" placeholder="请输入内容">{{content.content}}</textarea>
    </div>

    <button type="submit" class="btn btn-default">提交</button>
</form>

{% endblock %}