/**
 * Created by 毅 on 2016/8/28.
 */

const express = require("express");
const router = express.Router();
const User = require("../models/User");
const Content = require("../models/Content");
const mailService = require("../utils/mail");
const passwordUtil = require("../utils/password");
const crypto = require("crypto");
const jwtUtil = require("../utils/jwt");
const alipayConfig = require("../config/alipayConfig");

//统一返回格式
var responseData;

router.use(function (req, res, next) {
  responseData = {
    code: 0,
    message: "",
  };

  next();
});

/**
 * 生成随机令牌
 * @returns {string} - 随机令牌
 */
function generateToken() {
  return crypto.randomBytes(32).toString("hex");
}

/*
 * 用户注册
 *   注册逻辑
 *
 *   1.用户名不能为空
 *   2.密码不能为空
 *   3.两次输入密码必须一致
 *
 *   1.用户是否已经被注册了
 *       数据库查询
 *
 * */
router.post("/user/register", async function (req, res, next) {
  var username = req.body.username;
  var password = req.body.password;
  var repassword = req.body.repassword;
  var email = req.body.email || "";

  //用户是否为空
  if (username == "") {
    responseData.code = 1;
    responseData.message = "用户名不能为空";
    res.json(responseData);
    return;
  }
  //密码不能为空
  if (password == "") {
    responseData.code = 2;
    responseData.message = "密码不能为空";
    res.json(responseData);
    return;
  }
  //两次输入的密码必须一致
  if (password != repassword) {
    responseData.code = 3;
    responseData.message = "两次输入的密码不一致";
    res.json(responseData);
    return;
  }

  // 验证邮箱格式
  if (email && !validateEmail(email)) {
    responseData.code = 4;
    responseData.message = "邮箱格式不正确";
    res.json(responseData);
    return;
  }

  try {
    //用户名是否已经被注册了
    const userInfo = await User.findOne({ username: username });
    if (userInfo) {
      responseData.code = 5;
      responseData.message = "用户名已经被注册了";
      res.json(responseData);
      return;
    }

    // 邮箱是否已被注册
    if (email) {
      const emailUser = await User.findOne({ email: email });
      if (emailUser) {
        responseData.code = 6;
        responseData.message = "该邮箱已被注册";
        res.json(responseData);
        return;
      }
    }

    // 对密码进行加密
    const hashedPassword = passwordUtil.encrypt(password);

    // 生成验证令牌
    const verificationToken = generateToken();
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期

    //保存用户注册的信息到数据库中
    var user = new User({
      username: username,
      password: hashedPassword,
      email: email,
      verificationToken: verificationToken,
      verificationExpires: verificationExpires,
    });

    const newUser = await user.save();

    // 如果提供了邮箱，发送验证邮件
    if (email) {
      try {
        await mailService.sendVerificationMail(
          email,
          username,
          verificationToken
        );
        responseData.message = "注册成功，请查收验证邮件";
      } catch (err) {
        console.error("发送验证邮件失败:", err);
        responseData.message = "注册成功，但发送验证邮件失败，请联系管理员";
      }
    } else {
      responseData.message = "注册成功";
    }

    res.json(responseData);
  } catch (err) {
    console.error("注册过程中出错:", err);
    responseData.code = 7;
    responseData.message = "注册失败，请稍后重试";
    res.json(responseData);
  }
});

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} - 是否有效
 */
function validateEmail(email) {
  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
}

/**
 * 邮箱验证
 */
router.get("/user/verify", async function (req, res) {
  const email = req.query.email;
  const token = req.query.token;

  if (!email || !token) {
    return res.render("main/message", {
      message: "验证链接无效",
    });
  }

  try {
    // 查找对应的用户
    const user = await User.findOne({
      email: email,
      verificationToken: token,
      verificationExpires: { $gt: Date.now() }, // 验证令牌未过期
    });

    if (!user) {
      return res.render("main/message", {
        message: "验证链接无效或已过期",
      });
    }

    // 更新用户验证状态
    user.isVerified = true;
    user.verificationToken = "";
    user.verificationExpires = null;
    await user.save();

    res.render("main/message", {
      message: "邮箱验证成功，请登录",
    });
  } catch (err) {
    console.error("验证邮箱时出错:", err);
    res.render("main/message", {
      message: "验证过程中出错，请稍后重试",
    });
  }
});

/**
 * 请求重置密码
 */
router.post("/user/forgot-password", async function (req, res) {
  const email = req.body.email;

  if (!email || !validateEmail(email)) {
    responseData.code = 1;
    responseData.message = "请提供有效的邮箱地址";
    return res.json(responseData);
  }

  try {
    // 查找用户
    const user = await User.findOne({ email: email });
    if (!user) {
      // 为了安全，不告诉用户该邮箱是否存在
      responseData.message = "如果该邮箱已注册，我们将发送重置密码的链接";
      return res.json(responseData);
    }

    // 生成重置令牌
    const resetToken = generateToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1小时后过期

    // 更新用户信息
    user.resetPasswordToken = resetToken;
    user.resetPasswordExpires = resetExpires;
    await user.save();

    // 发送重置密码邮件
    await mailService.sendPasswordResetMail(email, user.username, resetToken);

    responseData.message = "重置密码链接已发送到您的邮箱";
    res.json(responseData);
  } catch (err) {
    console.error("请求重置密码时出错:", err);
    responseData.code = 2;
    responseData.message = "请求重置密码失败，请稍后重试";
    res.json(responseData);
  }
});

/**
 * 重置密码
 */
router.post("/user/reset-password", async function (req, res) {
  const email = req.body.email;
  const token = req.body.token;
  const password = req.body.password;
  const repassword = req.body.repassword;

  if (!email || !token || !password) {
    responseData.code = 1;
    responseData.message = "请提供所有必要信息";
    return res.json(responseData);
  }

  if (password !== repassword) {
    responseData.code = 2;
    responseData.message = "两次输入的密码不一致";
    return res.json(responseData);
  }

  try {
    // 查找用户
    const user = await User.findOne({
      email: email,
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() }, // 令牌未过期
    });

    if (!user) {
      responseData.code = 3;
      responseData.message = "重置链接无效或已过期";
      return res.json(responseData);
    }

    // 更新密码
    user.password = passwordUtil.encrypt(password);
    user.resetPasswordToken = "";
    user.resetPasswordExpires = null;
    await user.save();

    // 发送密码已重置通知
    try {
      await mailService.sendNotificationMail(
        email,
        "密码重置成功",
        `<p>您的密码已成功重置。如果这不是您本人的操作，请立即联系我们。</p>`
      );
    } catch (err) {
      console.error("发送密码重置通知失败:", err);
    }

    responseData.message = "密码重置成功，请使用新密码登录";
    res.json(responseData);
  } catch (err) {
    console.error("重置密码时出错:", err);
    responseData.code = 4;
    responseData.message = "重置密码失败，请稍后重试";
    res.json(responseData);
  }
});

/*
 * 登录
 * */
router.post("/user/login", async function (req, res) {
  var username = req.body.username;
  var password = req.body.password;

  if (username == "" || password == "") {
    responseData.code = 1;
    responseData.message = "用户名和密码不能为空";
    res.json(responseData);
    return;
  }

  try {
    // 查询数据库中用户名记录
    const userInfo = await User.findOne({ username: username });

    if (!userInfo) {
      responseData.code = 2;
      responseData.message = "用户名或密码错误";
      return res.json(responseData);
    }

    // 验证密码是否正确
    const isPasswordValid = passwordUtil.compare(password, userInfo.password);

    if (!isPasswordValid) {
      responseData.code = 2;
      responseData.message = "用户名或密码错误";
      return res.json(responseData);
    }

    // 如果用户有邮箱但未验证
    // if (userInfo.email && !userInfo.isVerified) {
    //     responseData.code = 3;
    //     responseData.message = '请先验证您的邮箱';
    //     return res.json(responseData);
    // }

    // 用户名和密码是正确的
    responseData.message = "登录成功";
    responseData.userInfo = {
      _id: userInfo._id,
      username: userInfo.username,
    };
    req.cookies.set(
      "userInfo",
      JSON.stringify({
        _id: userInfo._id,
        username: userInfo.username,
      })
    );
    res.json(responseData);
  } catch (err) {
    console.error("登录时出错:", err);
    responseData.code = 4;
    responseData.message = "登录失败，请稍后重试";
    res.json(responseData);
  }
});

/**
 * 管理员登录 - 使用JWT认证
 */
router.post("/admin/login", async function (req, res) {
  var username = req.body.username;
  var password = req.body.password;

  if (username == "" || password == "") {
    responseData.code = 1;
    responseData.message = "用户名和密码不能为空";
    return res.json(responseData);
  }

  try {
    // 查询数据库中用户名记录
    const userInfo = await User.findOne({ username: username });

    if (!userInfo) {
      responseData.code = 2;
      responseData.message = "用户名或密码错误";
      return res.json(responseData);
    }

    // 验证密码是否正确
    const isPasswordValid = passwordUtil.compare(password, userInfo.password);

    if (!isPasswordValid) {
      responseData.code = 2;
      responseData.message = "用户名或密码错误";
      return res.json(responseData);
    }

    // 验证是否是管理员
    if (!userInfo.isAdmin) {
      responseData.code = 3;
      responseData.message = "您没有管理员权限";
      return res.json(responseData);
    }

    // 生成JWT令牌
    const token = jwtUtil.generateToken({
      _id: userInfo._id,
      username: userInfo.username,
      isAdmin: userInfo.isAdmin,
    });

    // 设置cookie中的JWT令牌（用于前端请求认证）
    req.cookies.set("adminToken", token, {
      httpOnly: true,
      // 在生产环境中应设为true
      secure: false,
      maxAge: 24 * 60 * 60 * 1000, // 1天
    });

    // 返回成功信息和令牌
    responseData.message = "管理员登录成功";
    responseData.adminInfo = {
      _id: userInfo._id,
      username: userInfo.username,
      isAdmin: userInfo.isAdmin,
    };
    responseData.token = token;

    res.json(responseData);
  } catch (err) {
    console.error("管理员登录时出错:", err);
    responseData.code = 4;
    responseData.message = "登录失败，请稍后重试";
    res.json(responseData);
  }
});

/**
 * 管理员退出
 */
router.get("/admin/logout", function (req, res) {
  req.cookies.set("adminToken", null);
  responseData.message = "已成功退出管理员账号";
  res.json(responseData);
});

/*
 * 退出
 * */
router.get("/user/logout", function (req, res) {
  req.cookies.set("userInfo", null);
  res.json(responseData);
});

/*
 * 获取指定文章的所有评论
 * */
router.get("/comment", function (req, res) {
  var contentId = req.query.contentid || "";

  Content.findOne({
    _id: contentId,
  }).then(function (content) {
    responseData.data = content.comments;
    res.json(responseData);
  });
});

/*
 * 评论提交
 * */
router.post("/comment/post", function (req, res) {
  //内容的id
  var contentId = req.body.contentid || "";
  var postData = {
    username: req.userInfo.username,
    postTime: new Date(),
    content: req.body.content,
  };

  //查询当前这篇内容的信息
  Content.findOne({
    _id: contentId,
  })
    .then(function (content) {
      content.comments.push(postData);
      return content.save();
    })
    .then(function (newContent) {
      responseData.message = "评论成功";
      responseData.data = newContent;
      res.json(responseData);
    });
});

/**
 * 发送联系我们邮件
 */
router.post("/contact", async function (req, res) {
  const name = req.body.name;
  const email = req.body.email;
  const subject = req.body.subject;
  const message = req.body.message;

  if (!name || !email || !subject || !message) {
    responseData.code = 1;
    responseData.message = "请填写所有必填字段";
    return res.json(responseData);
  }

  if (!validateEmail(email)) {
    responseData.code = 2;
    responseData.message = "请提供有效的邮箱地址";
    return res.json(responseData);
  }

  try {
    // 发送邮件给管理员
    await mailService.sendHtmlMail(
      "<EMAIL>", // 管理员邮箱
      `网站联系表单: ${subject}`,
      `
                <h3>新的联系表单提交</h3>
                <p><strong>姓名:</strong> ${name}</p>
                <p><strong>邮箱:</strong> ${email}</p>
                <p><strong>主题:</strong> ${subject}</p>
                <p><strong>消息:</strong></p>
                <p>${message}</p>
            `
    );

    // 发送确认邮件给用户
    await mailService.sendHtmlMail(
      email,
      "我们已收到您的消息",
      `
                <div style="background-color: #f7f7f7; padding: 20px;">
                    <div style="max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 5px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                        <h2 style="color: #333; text-align: center;">感谢您的留言</h2>
                        <p>尊敬的 ${name}：</p>
                        <p>我们已收到您的消息，主题为"${subject}"。我们会尽快回复您。</p>
                        <p>以下是您提交的消息：</p>
                        <div style="background-color: #f9f9f9; padding: 15px; border-left: 4px solid #ccc; margin: 20px 0;">
                            ${message}
                        </div>
                        <p>如有任何疑问，请随时与我们联系。</p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                        <p style="color: #777; font-size: 12px; text-align: center;">此邮件由系统自动发送，请勿回复。</p>
                    </div>
                </div>
            `
    );

    responseData.message = "消息已发送，我们会尽快回复您";
    res.json(responseData);
  } catch (err) {
    console.error("发送联系表单邮件时出错:", err);
    responseData.code = 3;
    responseData.message = "发送消息失败，请稍后重试";
    res.json(responseData);
  }
});

/**
 * 检查管理员令牌是否有效
 */
router.get("/admin/check-auth", async function (req, res) {
  const token = req.headers.authorization?.replace("Bearer ", "");

  if (!token) {
    return res.status(401).json({
      code: 401,
      message: "未提供认证令牌",
    });
  }

  // 验证令牌
  const decoded = jwtUtil.verifyToken(token);

  if (!decoded) {
    return res.status(401).json({
      code: 401,
      message: "认证令牌无效或已过期",
    });
  }

  try {
    // 验证用户是否存在且是管理员
    const admin = await User.findById(decoded._id);

    if (!admin || !admin.isAdmin) {
      return res.status(403).json({
        code: 403,
        message: "您没有管理员权限",
      });
    }

    // 令牌有效且用户是管理员
    res.json({
      code: 0,
      message: "认证成功",
      adminInfo: {
        _id: admin._id,
        username: admin.username,
        isAdmin: admin.isAdmin,
      },
    });
  } catch (error) {
    console.error("检查管理员认证时出错:", error);
    res.status(500).json({
      code: 500,
      message: "服务器内部错误",
    });
  }
});

// Create payment
router.post("/alipay/create", async (req, res) => {
  try {
    const { orderId, totalAmount } = req.body;

    // 生成支付链接
    const paymentUrl =
      alipayConfig.gateway +
      "?" +
      new URLSearchParams({
        app_id: alipayConfig.appId,
        method: "alipay.trade.page.pay",
        format: "JSON",
        charset: "utf-8",
        sign_type: "RSA2",
        timestamp: new Date().toISOString().slice(0, 19).replace("T", " "),
        version: "1.0",
        return_url: alipayConfig.returnUrl,
        notify_url: alipayConfig.notifyUrl,
        biz_content: JSON.stringify({
          out_trade_no: orderId,
          product_code: "FAST_INSTANT_TRADE_PAY",
          total_amount: totalAmount,
          subject: "订单支付-" + orderId,
        }),
      });

    res.json({ payUrl: paymentUrl });
  } catch (err) {
    console.error("Alipay payment creation error:", err);
    res.status(500).json({ error: "支付创建失败" });
  }
});

// Payment return handler
router.get("/alipay/return", (req, res) => {
  // 直接显示支付成功
  res.send(`
        <script>
            alert('支付成功！正在返回订单列表...');
            window.location.href = '/admin/order';
        </script>
    `);
});

module.exports = router;
