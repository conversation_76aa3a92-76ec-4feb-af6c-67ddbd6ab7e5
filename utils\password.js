/**
 * 密码加密和验证工具
 */

const bcrypt = require('bcryptjs');

/**
 * 对密码进行加密
 * @param {string} password - 原始密码
 * @returns {string} - 加密后的密码
 */
exports.encrypt = function(password) {
    // 生成盐值，默认10轮
    const salt = bcrypt.genSaltSync(10);
    // 返回加密后的密码
    return bcrypt.hashSync(password, salt);
};

/**
 * 验证密码是否正确
 * @param {string} password - 用户输入的原始密码
 * @param {string} hash - 数据库中存储的加密密码
 * @returns {boolean} - 密码是否匹配
 */
exports.compare = function(password, hash) {
    return bcrypt.compareSync(password, hash);
}; 