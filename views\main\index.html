{% extends 'layout.html' %}

{% block main %}
    {% if contents.length > 0 %}
    <div class="row">
        {% for content in contents %}
        <div class="col-sm-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <a href="/view?contentid={{content.id}}">{{content.title}}</a>
                    </h3>
                </div>
                <div class="panel-body">
                    <p class="text-muted">
                        作者：<span>{{content.user.username}}</span> |
                        时间：<span>{{content.addTime|date('Y年m月d日 H:i:s', -8)}}</span> |
                        阅读：<span>{{content.views}}</span> |
                        评论：<span>{{content.comments.length}}</span>
                    </p>
                    <p>{{content.description}}</p>
                </div>
                <div class="panel-footer text-right">
                    <a href="/view?contentid={{content.id}}" class="btn btn-primary btn-sm">阅读全文</a>
                </div>
            </div>
        </div>
        {% if loop.index % 2 == 0 %}
        <div class="clearfix"></div>
        {% endif %}
        {% endfor %}
    </div>

    <nav aria-label="Page navigation" class="text-center">
        <ul class="pagination">
            {% if page <= 1 %}
            <li class="disabled">
                <span aria-hidden="true">&laquo;</span>
            </li>
            {% else %}
            <li>
                <a href="/?category={{category}}&page={{page-1}}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% for i in range(1, pages + 1) %}
            <li {% if page == i %}class="active"{% endif %}>
                <a href="/?category={{category}}&page={{i}}">{{i}}</a>
            </li>
            {% endfor %}

            {% if page >= pages %}
            <li class="disabled">
                <span aria-hidden="true">&raquo;</span>
            </li>
            {% else %}
            <li>
                <a href="/?category={{category}}&page={{page+1}}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% else %}
    <div class="alert alert-info" role="alert">
        还没有内容，请先添加内容
    </div>
    {% endif %}
{% endblock %}