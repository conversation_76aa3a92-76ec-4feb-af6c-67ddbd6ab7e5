/**
 * 订单模型
 */
var mongoose = require('mongoose');
var Schema = mongoose.Schema;

// 订单表结构
var orderSchema = new Schema({
    // 订单号
    orderNumber: {
        type: String,
        required: true,
        unique: true
    },
    
    // 客户名称
    customerName: {
        type: String,
        required: true
    },
    
    // 联系电话
    phone: {
        type: String
    },
    
    // 订单商品
    items: [{
        name: String,
        price: Number,
        quantity: Number
    }],
    
    // 订单总价
    totalPrice: {
        type: Number,
        required: true
    },
    
    // 订单状态 (1: 待付款, 2: 已付款, 3: 已发货, 4: 已完成)
    status: {
        type: Number,
        default: 1
    },
    
    // 创建时间
    createTime: {
        type: Date,
        default: Date.now
    },
    
    // 更新时间
    updateTime: {
        type: Date,
        default: Date.now
    },
    
    // 软删除标记 (true: 已删除, false: 未删除)
    isDeleted: {
        type: Boolean,
        default: false
    },
    
    // 删除时间
    deleteTime: {
        type: Date
    }
});

module.exports = mongoose.model('Order', orderSchema); 