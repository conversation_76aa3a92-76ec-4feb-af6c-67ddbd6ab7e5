/**
 * 订单管理脚本
 */
$(function() {
    // 模拟订单数据
    var orders = [
        {
            id: '1',
            orderNumber: 'ORD20250501001',
            customerName: '张三',
            phone: '13800138001',
            totalPrice: 299.99,
            status: 2, // 已付款
            createTime: '2025-05-01 10:21:33',
            isDeleted: false,
            items: [
                { name: '商品A', price: 99.99, quantity: 2 },
                { name: '商品B', price: 100.01, quantity: 1 }
            ]
        },
        {
            id: '2',
            orderNumber: 'ORD20250502002',
            customerName: '李四',
            phone: '13900139002',
            totalPrice: 599.50,
            status: 3, // 已发货
            createTime: '2025-05-02 14:33:21',
            isDeleted: false,
            items: [
                { name: '商品C', price: 199.50, quantity: 3 }
            ]
        },
        {
            id: '3',
            orderNumber: 'ORD20250503003',
            customerName: '王五',
            phone: '13700137003',
            totalPrice: 999.00,
            status: 1, // 待付款
            createTime: '2025-05-03 09:15:42',
            isDeleted: false,
            items: [
                { name: '商品D', price: 399.00, quantity: 1 },
                { name: '商品E', price: 300.00, quantity: 2 }
            ]
        },
        {
            id: '4',
            orderNumber: 'ORD20250504004',
            customerName: '赵六',
            phone: '13600136004',
            totalPrice: 1299.99,
            status: 4, // 已完成
            createTime: '2025-05-04 16:45:10',
            isDeleted: false,
            items: [
                { name: '商品F', price: 1299.99, quantity: 1 }
            ]
        },
        {
            id: '5',
            orderNumber: 'ORD20250505005',
            customerName: '钱七',
            phone: '13500135005',
            totalPrice: 459.97,
            status: 2, // 已付款
            createTime: '2025-05-05 11:22:33',
            isDeleted: false,
            items: [
                { name: '商品A', price: 99.99, quantity: 1 },
                { name: '商品C', price: 199.50, quantity: 1 },
                { name: '商品E', price: 160.48, quantity: 1 }
            ]
        }
    ];
    
    // 显示已删除订单标志
    var showDeleted = false;
    
    // 当前操作的订单ID
    var currentOrderId = null;
    
    // 刷新订单列表
    function refreshOrderList() {
        var $orderList = $('#orderList');
        $orderList.empty();
        
        // 过滤订单
        var filteredOrders = orders.filter(function(order) {
            return showDeleted ? true : !order.isDeleted;
        });
        
        if (filteredOrders.length === 0) {
            $orderList.html('<tr><td colspan="7" class="text-center">暂无订单数据</td></tr>');
            return;
        }
        
        // 填充订单数据
        filteredOrders.forEach(function(order) {
            var statusText = '';
            var statusClass = '';
            
            switch(order.status) {
                case 1:
                    statusText = '待付款';
                    statusClass = 'warning';
                    break;
                case 2:
                    statusText = '已付款';
                    statusClass = 'info';
                    break;
                case 3:
                    statusText = '已发货';
                    statusClass = 'primary';
                    break;
                case 4:
                    statusText = '已完成';
                    statusClass = 'success';
                    break;
            }
            
            var row = '<tr class="' + (order.isDeleted ? 'danger' : '') + '">' +
                '<td>' + order.orderNumber + '</td>' +
                '<td>' + order.customerName + '</td>' +
                '<td>' + order.phone + '</td>' +
                '<td>¥' + order.totalPrice.toFixed(2) + '</td>' +
                '<td><span class="label label-' + statusClass + '">' + statusText + '</span></td>' +
                '<td>' + order.createTime + '</td>' +
                '<td>';
            
            // 查看按钮
            row += '<button class="btn btn-xs btn-info view-order" data-id="' + order.id + '">' +
                '<i class="fa fa-eye"></i> 查看</button> ';
            
            // 删除/恢复按钮
            if (order.isDeleted) {
                row += '<button class="btn btn-xs btn-success restore-order" data-id="' + order.id + '">' +
                    '<i class="fa fa-undo"></i> 恢复</button>';
            } else {
                row += '<button class="btn btn-xs btn-danger delete-order" data-id="' + order.id + '">' +
                    '<i class="fa fa-trash"></i> 删除</button>';
            }
            
            row += '</td></tr>';
            
            $orderList.append(row);
        });
    }
    
    // 初始加载订单列表
    refreshOrderList();
    
    // 切换显示/隐藏已删除订单
    $('#showDeleted').on('click', function() {
        showDeleted = !showDeleted;
        $(this).html(showDeleted ? 
            '<i class="fa fa-eye-slash"></i> 隐藏已删除' : 
            '<i class="fa fa-eye"></i> 显示已删除');
        refreshOrderList();
    });
    
    // 刷新订单列表
    $('#refreshOrders').on('click', refreshOrderList);
    
    // 查看订单详情
    $('#orderList').on('click', '.view-order', function() {
        console.log('查看按钮被点击');
        var orderId = $(this).data('id');
        console.log('订单ID:', orderId);
        
        // 将orderId转换为字符串进行比较
        var order = orders.find(function(o) { 
            return String(o.id) === String(orderId); 
        });
        
        if (!order) {
            console.log('未找到对应订单');
            alert('未找到ID为 ' + orderId + ' 的订单，请刷新页面重试');
            return;
        }
        
        console.log('找到订单:', order);
        
        // 填充订单基本信息
        $('#detail-orderNumber').text(order.orderNumber);
        $('#detail-customerName').text(order.customerName);
        $('#detail-phone').text(order.phone);
        $('#detail-totalPrice').text('¥' + order.totalPrice.toFixed(2));
        $('#detail-createTime').text(order.createTime);
        
        // 设置订单状态
        var statusText = '';
        var statusClass = '';
        
        switch(order.status) {
            case 1:
                statusText = '待付款';
                statusClass = 'warning';
                // 显示支付按钮
                $('#alipayButton').show();
                break;
            case 2:
                statusText = '已付款';
                statusClass = 'info';
                break;
            case 3:
                statusText = '已发货';
                statusClass = 'primary';
                break;
            case 4:
                statusText = '已完成';
                statusClass = 'success';
                break;
        }
        
        $('#detail-status').html('<span class="label label-' + statusClass + '">' + statusText + '</span>');
        
        // 填充订单商品
        var $items = $('#detail-items');
        $items.empty();
        
        order.items.forEach(function(item) {
            var subtotal = item.price * item.quantity;
            var row = '<tr>' +
                '<td>' + item.name + '</td>' +
                '<td>¥' + item.price.toFixed(2) + '</td>' +
                '<td>' + item.quantity + '</td>' +
                '<td>¥' + subtotal.toFixed(2) + '</td>' +
                '</tr>';
            $items.append(row);
        });
        
        // 显示模态框
        $('#orderDetailModal').modal('show');
    });

    // 处理支付宝支付按钮点击
    $('#alipayButton').on('click', function() {
        alert('支付成功！正在返回订单列表...');
        // 修正路由路径
        window.location.href = '/admin/order';
    });
    
    // 删除订单
    $('#orderList').on('click', '.delete-order', function() {
        console.log('删除按钮被点击');
        var orderId = $(this).data('id');
        console.log('订单ID:', orderId);
        console.log('所有订单:', orders);
        
        // 将orderId转换为字符串进行比较
        var order = orders.find(function(o) { 
            console.log('比较:', o.id, orderId, typeof o.id, typeof orderId, o.id === orderId);
            return String(o.id) === String(orderId); 
        });
        
        if (!order) {
            console.log('未找到对应订单');
            alert('未找到ID为 ' + orderId + ' 的订单，请刷新页面重试');
            return;
        }
        
        console.log('找到订单:', order);
        
        // 设置当前操作的订单ID
        currentOrderId = orderId;
        
        // 显示确认模态框
        $('#delete-orderNumber').text(order.orderNumber);
        $('#deleteOrderModal').modal('show');
    });
    
    // 确认删除订单
    $('#confirmDelete').on('click', function() {
        console.log('确认删除按钮被点击');
        if (!currentOrderId) {
            console.log('没有当前操作的订单ID');
            return;
        }
        
        console.log('当前操作的订单ID:', currentOrderId);
        // 查找订单
        var orderIndex = orders.findIndex(function(o) { 
            return String(o.id) === String(currentOrderId); 
        });
        
        console.log('订单索引:', orderIndex);
        if (orderIndex !== -1) {
            // 软删除订单
            console.log('执行软删除操作');
            orders[orderIndex].isDeleted = true;
            orders[orderIndex].deleteTime = new Date().toLocaleString();
            
            // 刷新订单列表
            refreshOrderList();
            
            // 关闭模态框
            $('#deleteOrderModal').modal('hide');
            
            // 显示成功消息
            alert('订单已成功删除！');
        } else {
            console.log('未找到订单索引');
            alert('删除失败：未找到订单');
        }
    });
    
    // 恢复订单
    $('#orderList').on('click', '.restore-order', function() {
        console.log('恢复按钮被点击');
        var orderId = $(this).data('id');
        console.log('订单ID:', orderId);
        
        // 将orderId转换为字符串进行比较
        var order = orders.find(function(o) { 
            return String(o.id) === String(orderId); 
        });
        
        if (!order) {
            console.log('未找到对应订单');
            alert('未找到ID为 ' + orderId + ' 的订单，请刷新页面重试');
            return;
        }
        
        // 设置当前操作的订单ID
        currentOrderId = orderId;
        
        // 显示确认模态框
        $('#restore-orderNumber').text(order.orderNumber);
        $('#restoreOrderModal').modal('show');
    });
    
    // 确认恢复订单
    $('#confirmRestore').on('click', function() {
        console.log('确认恢复按钮被点击');
        if (!currentOrderId) {
            console.log('没有当前操作的订单ID');
            return;
        }
        
        console.log('当前操作的订单ID:', currentOrderId);
        // 查找订单
        var orderIndex = orders.findIndex(function(o) { 
            return String(o.id) === String(currentOrderId); 
        });
        
        console.log('订单索引:', orderIndex);
        if (orderIndex !== -1) {
            // 恢复订单
            console.log('执行恢复操作');
            orders[orderIndex].isDeleted = false;
            orders[orderIndex].deleteTime = null;
            
            // 刷新订单列表
            refreshOrderList();
            
            // 关闭模态框
            $('#restoreOrderModal').modal('hide');
            
            // 显示成功消息
            alert('订单已成功恢复！');
        } else {
            console.log('未找到订单索引');
            alert('恢复失败：未找到订单');
        }
    });
    
    // 测试按钮
    $('#testModal').on('click', function() {
        console.log('测试模态框按钮被点击');
        $('#deleteOrderModal').modal('show');
    });
    
    $('#testDelete').on('click', function() {
        console.log('测试删除功能按钮被点击');
        // 设置当前操作的订单ID为第一个订单
        currentOrderId = '1';
        var orderIndex = orders.findIndex(function(o) { return o.id === currentOrderId; });
        
        if (orderIndex !== -1) {
            console.log('找到订单，索引:', orderIndex);
            // 软删除订单
            orders[orderIndex].isDeleted = true;
            orders[orderIndex].deleteTime = new Date().toLocaleString();
            
            // 刷新订单列表
            refreshOrderList();
            
            alert('测试删除成功！');
        } else {
            console.log('未找到订单');
            alert('测试删除失败：未找到订单');
        }
    });
}); 