/**
 * Created by 毅 on 2016/8/28.
 */

var express = require('express');
var router = express.Router();
var path = require('path');

var User = require('../models/User');
var Category = require('../models/Category');
var Content = require('../models/Content');
var File = require('../models/File');
var Order = require('../models/Order');
const fs = require("fs");
const { upload, processImage, generateThumbnail } = require('../utils/upload');
const adminAuth = require('../middlewares/adminAuth');

// 管理员登录页面
router.get('/login', function(req, res) {
    // 如果已经登录，跳转到管理页面
    const token = req.cookies.get('adminToken');
    if (token) {
        return res.redirect('/admin');
    }
    
    res.render('admin/login', {
        userInfo: req.userInfo,
        message: req.query.message || ''
    });
});

// 应用管理员认证中间件到所有需要认证的路由
router.use(adminAuth);

/**
 * 首页
 */
router.get('/', function(req, res, next) {
    res.render('admin/index', {
        userInfo: req.userInfo,
        adminInfo: req.adminInfo
    });
});

/*
* 用户管理
* */
router.get('/user', function(req, res) {

    /*
    * 从数据库中读取所有的用户数据
    *
    * limit(Number) : 限制获取的数据条数
    *
    * skip(2) : 忽略数据的条数
    *
    * 每页显示2条
    * 1 : 1-2 skip:0 -> (当前页-1) * limit
    * 2 : 3-4 skip:2
    * */

    var page = Number(req.query.page || 1);
    var limit = 10;
    var pages = 0;

    User.count().then(function(count) {

        //计算总页数
        pages = Math.ceil(count / limit);
        //取值不能超过pages
        page = Math.min( page, pages );
        //取值不能小于1
        page = Math.max( page, 1 );

        var skip = (page - 1) * limit;

        User.find().limit(limit).skip(skip).then(function(users) {
            res.render('admin/user_index', {
                userInfo: req.userInfo,
                users: users,

                count: count,
                pages: pages,
                limit: limit,
                page: page
            });
        });

    });

});

/*
* 分类首页
* */
router.get('/category', function(req, res) {

    var page = Number(req.query.page || 1);
    var limit = 10;
    var pages = 0;

    Category.count().then(function(count) {

        //计算总页数
        pages = Math.ceil(count / limit);
        //取值不能超过pages
        page = Math.min( page, pages );
        //取值不能小于1
        page = Math.max( page, 1 );

        var skip = (page - 1) * limit;

        /*
        * 1: 升序
        * -1: 降序
        * */
        Category.find().sort({_id: -1}).limit(limit).skip(skip).then(function(categories) {
            res.render('admin/category_index', {
                userInfo: req.userInfo,
                categories: categories,

                count: count,
                pages: pages,
                limit: limit,
                page: page
            });
        });

    });

});

/*
* 分类的添加
* */
router.get('/category/add', function(req, res) {
    res.render('admin/category_add', {
        userInfo: req.userInfo
    });
});

/*
* 分类的保存
* */
router.post('/category/add', function(req, res) {

    var name = req.body.name || '';

    if (name == '') {
        res.render('admin/error', {
            userInfo: req.userInfo,
            message: '名称不能为空'
        });
        return;
    }

    //数据库中是否已经存在同名分类名称
    Category.findOne({
        name: name
    }).then(function(rs) {
        if (rs) {
            //数据库中已经存在该分类了
            res.render('admin/error', {
                userInfo: req.userInfo,
                message: '分类已经存在了'
            })
            return Promise.reject();
        } else {
            //数据库中不存在该分类，可以保存
            return new Category({
                name: name
            }).save();
        }
    }).then(function(newCategory) {
        res.render('admin/success', {
            userInfo: req.userInfo,
            message: '分类保存成功',
            url: '/admin/category'
        });
    })

});

/*
* 分类修改
* */
router.get('/category/edit', function(req, res) {

    //获取要修改的分类的信息，并且用表单的形式展现出来
    var id = req.query.id || '';

    //获取要修改的分类信息
    Category.findOne({
        _id: id
    }).then(function(category) {
        if (!category) {
            res.render('admin/error', {
                userInfo: req.userInfo,
                message: '分类信息不存在'
            });
        } else {
            res.render('admin/category_edit', {
                userInfo: req.userInfo,
                category: category
            });
        }
    })

});

/*
* 分类的修改保存
* */
router.post('/category/edit', function(req, res) {

    //获取要修改的分类的信息，并且用表单的形式展现出来
    var id = req.query.id || '';
    //获取post提交过来的名称
    var name = req.body.name || '';

    //获取要修改的分类信息
    Category.findOne({
        _id: id
    }).then(function(category) {
        if (!category) {
            res.render('admin/error', {
                userInfo: req.userInfo,
                message: '分类信息不存在'
            });
            return Promise.reject();
        } else {
            //当用户没有做任何的修改提交的时候
            if (name == category.name) {
                res.render('admin/success', {
                    userInfo: req.userInfo,
                    message: '修改成功',
                    url: '/admin/category'
                });
                return Promise.reject();
            } else {
                //要修改的分类名称是否已经在数据库中存在
                return Category.findOne({
                    _id: {$ne: id},
                    name: name
                });
            }
        }
    }).then(function(sameCategory) {
        if (sameCategory) {
            res.render('admin/error', {
                userInfo: req.userInfo,
                message: '数据库中已经存在同名分类'
            });
            return Promise.reject();
        } else {
            return Category.update({
                _id: id
            }, {
                name: name
            });
        }
    }).then(function() {
        res.render('admin/success', {
            userInfo: req.userInfo,
            message: '修改成功',
            url: '/admin/category'
        });
    })

});

/*
* 分类删除
* */
router.get('/category/delete', function(req, res) {

    //获取要删除的分类的id
    var id = req.query.id || '';

    Category.remove({
        _id: id
    }).then(function() {
        res.render('admin/success', {
            userInfo: req.userInfo,
            message: '删除成功',
            url: '/admin/category'
        });
    });

});

/*
* 内容首页
* */
router.get('/content', function(req, res) {

    var page = Number(req.query.page || 1);
    var limit = 10;
    var pages = 0;

    Content.count().then(function(count) {

        //计算总页数
        pages = Math.ceil(count / limit);
        //取值不能超过pages
        page = Math.min( page, pages );
        //取值不能小于1
        page = Math.max( page, 1 );

        var skip = (page - 1) * limit;

        Content.find().limit(limit).skip(skip).populate(['category', 'user']).sort({
            addTime: -1
        }).then(function(contents) {
            res.render('admin/content_index', {
                userInfo: req.userInfo,
                contents: contents,

                count: count,
                pages: pages,
                limit: limit,
                page: page
            });
        });

    });

});

/*
 * 内容添加页面
 * */
router.get('/content/add', function(req, res) {

    Category.find().sort({_id: -1}).then(function(categories) {
        res.render('admin/content_add', {
            userInfo: req.userInfo,
            categories: categories
        })
    });

});

/*
* 内容保存
* */
router.post('/content/add', function(req, res) {

    //console.log(req.body)

    if ( req.body.category == '' ) {
        res.render('admin/error', {
            userInfo: req.userInfo,
            message: '内容分类不能为空'
        })
        return;
    }

    if ( req.body.title == '' ) {
        res.render('admin/error', {
            userInfo: req.userInfo,
            message: '内容标题不能为空'
        })
        return;
    }

    //保存数据到数据库
    new Content({
        category: req.body.category,
        title: req.body.title,
        user: req.userInfo._id.toString(),
        description: req.body.description,
        content: req.body.content
    }).save().then(function(rs) {
        res.render('admin/success', {
            userInfo: req.userInfo,
            message: '内容保存成功',
            url: '/admin/content'
        })
    });

});

/*
* 修改内容
* */
router.get('/content/edit', function(req, res) {

    var id = req.query.id || '';

    var categories = [];

    Category.find().sort({_id: 1}).then(function(rs) {

        categories = rs;

        return Content.findOne({
            _id: id
        }).populate('category');
    }).then(function(content) {

        if (!content) {
            res.render('admin/error', {
                userInfo: req.userInfo,
                message: '指定内容不存在'
            });
            return Promise.reject();
        } else {
            res.render('admin/content_edit', {
                userInfo: req.userInfo,
                categories: categories,
                content: content
            })
        }
    });

});

/*
 * 保存修改内容
 * */
router.post('/content/edit', function(req, res) {
    var id = req.query.id || '';

    if ( req.body.category == '' ) {
        res.render('admin/error', {
            userInfo: req.userInfo,
            message: '内容分类不能为空'
        })
        return;
    }

    if ( req.body.title == '' ) {
        res.render('admin/error', {
            userInfo: req.userInfo,
            message: '内容标题不能为空'
        })
        return;
    }

    Content.update({
        _id: id
    }, {
        category: req.body.category,
        title: req.body.title,
        description: req.body.description,
        content: req.body.content
    }).then(function() {
        res.render('admin/success', {
            userInfo: req.userInfo,
            message: '内容保存成功',
            url: '/admin/content/edit?id=' + id
        })
    });

});

/*
* 内容删除
* */
router.get('/content/delete', function(req, res) {
    var id = req.query.id || '';

    Content.remove({
        _id: id
    }).then(function() {
        res.render('admin/success', {
            userInfo: req.userInfo,
            message: '删除成功',
            url: '/admin/content'
        });
    });
});

/**
 * 文件上传页面
 */
router.get('/upload', function(req, res) {
    res.render('admin/files_upload', {
        userInfo: req.userInfo
    });
});

/**
 * 处理单文件上传
 */
router.post('/upload/single', upload.single('file'), async function(req, res) {
    try {
        if (!req.file) {
            throw new Error('请选择要上传的文件');
        }

        // 处理图片
        const { processedPath } = await processImage(req.file.path, {
            width: 800,
            height: 600,
            quality: 80
        });

        // 生成缩略图
        const thumbnailPath = await generateThumbnail(req.file.path);

        // 保存文件信息到数据库
        const file = new File({
            originalName: req.file.originalname,
            filename: req.file.filename,
            originalPath: '/uploads/' + path.basename(req.file.path),
            processedPath: '/uploads/' + path.basename(processedPath),
            thumbnailPath: '/uploads/' + path.basename(thumbnailPath),
            size: req.file.size,
            mimeType: req.file.mimetype,
            user: req.userInfo._id
        });

        await file.save();

        res.json({
            success: true,
            message: '文件上传成功',
            data: file
        });
    } catch (error) {
        res.json({
            success: false,
            message: error.message
        });
    }
});

/**
 * 处理多文件上传
 */
router.post('/upload/multiple', upload.array('files', 5), async function(req, res) {
    try {
        if (!req.files || req.files.length === 0) {
            throw new Error('请选择要上传的文件');
        }

        const results = [];

        // 处理每个上传的文件
        for (const uploadedFile of req.files) {
            const { processedPath } = await processImage(uploadedFile.path, {
                width: 800,
                height: 600,
                quality: 80
            });

            const thumbnailPath = await generateThumbnail(uploadedFile.path);

            // 保存文件信息到数据库
            const file = new File({
                originalName: uploadedFile.originalname,
                filename: uploadedFile.filename,
                originalPath: '/uploads/' + path.basename(uploadedFile.path),
                processedPath: '/uploads/' + path.basename(processedPath),
                thumbnailPath: '/uploads/' + path.basename(thumbnailPath),
                size: uploadedFile.size,
                mimeType: uploadedFile.mimetype,
                user: req.userInfo._id
            });

            await file.save();
            results.push(file);
        }

        res.json({
            success: true,
            message: '文件上传成功',
            data: results
        });
    } catch (error) {
        res.json({
            success: false,
            message: error.message
        });
    }
});

/**
 * 获取文件列表
 */
router.get('/api/files', async function(req, res) {
    try {
        const files = await File.find()
            .sort({ uploadTime: -1 })
            .populate('user', 'username');

        res.json({
            success: true,
            files: files
        });
    } catch (error) {
        res.json({
            success: false,
            message: error.message
        });
    }
});

/**
 * 删除文件
 */
router.delete('/api/files/:id', async function(req, res) {
    try {
        const file = await File.findById(req.params.id);
        
        if (!file) {
            throw new Error('文件不存在');
        }

        // 删除物理文件
        const filePaths = [
            path.join(__dirname, '..', file.originalPath),
            path.join(__dirname, '..', file.processedPath),
            path.join(__dirname, '..', file.thumbnailPath)
        ];

        for (const filePath of filePaths) {
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }

        // 从数据库中删除记录
        await File.deleteOne({ _id: req.params.id });

        res.json({
            success: true,
            message: '文件删除成功'
        });
    } catch (error) {
        res.json({
            success: false,
            message: error.message
        });
    }
});

/**
 * 订单管理页面
 */
router.get('/order', function(req, res) {
    res.render('admin/order_index', {
        userInfo: req.userInfo,
        adminInfo: req.adminInfo
    });
});

/**
 * 获取订单列表API
 */
router.get('/api/orders', async function(req, res) {
    try {
        // 这里我们只实现前端的软删除功能，所以不需要真正查询数据库
        // 返回模拟数据
        res.json({
            success: true,
            orders: []  // 前端会提供默认数据
        });
    } catch (error) {
        res.json({
            success: false,
            message: error.message
        });
    }
});

router.get("/uploads", function(req, res, next) {
    res.render('admin/files_uploads', {
        userInfo: req.userInfo
    });
});

module.exports = router;