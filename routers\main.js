/**
 * Created by 毅 on 2016/8/28.
 */

var express = require('express');
var router = express.Router();

var Category = require('../models/Category');
var Content = require('../models/Content');
const mailService = require("../utils/mail");

var data;




/*
* 处理通用的数据
* */
router.use(function (req, res, next) {
    data = {
        userInfo: req.userInfo,
        categories: []
    }

    Category.find().then(function(categories) {
        data.categories = categories;
        next();
    });
});



/*
* 首页
* */
router.get('/', function(req, res, next) {
    console.log("abc")
    data.category = req.query.category || '';
    data.count = 0;
    data.page = Number(req.query.page || 1);
    data.limit = 10;
    data.pages = 0;

    var where = {};
    if (data.category) {
        where.category = data.category
    }

    Content.where(where).count().then(function(count) {

        data.count = count;
        //计算总页数
        data.pages = Math.ceil(data.count / data.limit);
        //取值不能超过pages
        data.page = Math.min( data.page, data.pages );
        //取值不能小于1
        data.page = Math.max( data.page, 1 );

        var skip = (data.page - 1) * data.limit;

        return Content.where(where).find().limit(data.limit).skip(skip).populate(['category', 'user']).sort({
            addTime: -1
        });

    }).then(function(contents) {
        data.contents = contents;
        res.render('main/index', data);
    })
});


router.get('/mail', async function (req,res){
    await mailService.sendTextMail(
        "<EMAIL>",
        "测试邮件",
        "这是一封测试邮件，用于验证邮件发送功能是否正常工作。"
    );
    res.send("邮件已发送")
});


/**
 * 联系我们页面
 */
router.get('/contact', function(req, res) {
    res.render('main/contact', data);
});


/**
 * 密码重置页面
 */
router.get('/reset-password', function(req, res) {
    const email = req.query.email || '';
    const token = req.query.token || '';
    
    res.render('main/reset-password', {
        userInfo: req.userInfo,
        email: email,
        token: token
    });
});


router.get('/view', function (req, res){

    var contentId = req.query.contentid || '';

    Content.findOne({
        _id: contentId
    }).then(function (content) {
        data.content = content;

        content.views++;
        content.save();

        res.render('main/view', data);
    });

});

module.exports = router;