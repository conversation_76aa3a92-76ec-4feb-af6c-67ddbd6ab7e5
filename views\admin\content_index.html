{% extends 'layout.html' %}

{% block main %}

<ol class="breadcrumb">
    <li><a href="/">管理首页</a></li>
    <li><span>内容列表</span></li>
</ol>

<h3>内容列表</h3>

{% include 'page.html' %}

<table class="table table-hover table-striped">

    <tr>
        <th>ID</th>
        <th>分类名称</th>
        <th>标题</th>
        <th>作者</th>
        <th>添加时间</th>
        <th>阅读量</th>
        <th>操作</th>
    </tr>

    {% for content in contents %}
    <tr>
        <td>{{content._id.toString()}}</td>
        <td>{{content.category.name}}</td>
        <td>{{content.title}}</td>
        <td>{{content.user.username}}</td>
        <td>{{content.addTime|date('Y年m月d日 H:i:s', -8*60)}}</td>
        <td>{{content.views}}</td>
        <td>
            <a href="/admin/content/edit?id={{content._id.toString()}}">修改</a> |
            <a href="/admin/content/delete?id={{content._id.toString()}}">删除</a>
        </td>
    </tr>
    {% endfor %}

</table>

{% include 'page.html' %}

{% endblock %}