﻿<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客</title>
    <link href="/public/css/bootstrap.min.css" rel="stylesheet">
    <link href="/public/css/bootstrap-theme.min.css" rel="stylesheet">
    <link href="/public/fontAwesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="/public/css/main.css" rel="stylesheet">
    <script src="/public/js/jquery-1.12.4.min.js"></script>
    <script src="/public/js/bootstrap.min.js"></script>
    <script src="/public/js/index.js"></script>
    <script src="/public/js/main.js"></script>
    {% block head %}{% endblock %}
</head>

<body>
    <!-- 顶部导航 -->
    <nav class="navbar navbar-default navbar-fixed-top">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="/">
                    <img src="/public/images/00002637.png" alt="博客Logo" height="30">
                </a>
            </div>

            <div class="collapse navbar-collapse" id="navbar">
                <ul class="nav navbar-nav">
                    <li {% if category == '' %}class="active"{% endif %}>
                        <a href="/">首页</a>
                    </li>
                    {% for cate in categories %}
                    <li {% if category == cate.id %}class="active"{% endif %}>
                        <a href="/?category={{cate.id}}">{{cate.name}}</a>
                    </li>
                    {% endfor %}
                    <li><a href="/contact">联系我们</a></li>
                </ul>

                <ul class="nav navbar-nav navbar-right">
                    {% if userInfo._id %}
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                            {{userInfo.username}} <span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu">
                            {% if userInfo.isAdmin %}
                            <li><a href="javascript:;" onclick="checkAdminAccess()">管理后台</a></li>
                            <li role="separator" class="divider"></li>
                            {% endif %}
                            <li><a href="javascript:;" id="logout">退出</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li><a href="javascript:;" data-toggle="modal" data-target="#loginModal">登录</a></li>
                    <li><a href="javascript:;" data-toggle="modal" data-target="#registerModal">注册</a></li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 头部图片 -->
    <header class="jumbotron" style="background: url('/public/images/IMG_0293.JPG') center center; background-size: cover; margin-bottom: 0;">
        <div class="container">
            <div style="height: 200px;"></div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <div class="container" style="margin-top: 20px;">
        <div class="row">
            <!-- 左侧主要内容 -->
            <div class="col-md-8">
                {% block main %}{% endblock %}
            </div>

            <!-- 右侧边栏 -->
            <div class="col-md-4">
                {% if userInfo._id %}
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">用户信息</h3>
                    </div>
                    <div class="panel-body">
                        <p>{{userInfo.username}}</p>
                        {% if userInfo.isAdmin %}
                        <p>
                            <span class="text-danger">你好，管理员！</span>
                            <a href="javascript:;" onclick="checkAdminAccess()" class="btn btn-primary btn-xs">进入管理</a>
                        </p>
                        {% else %}
                        <p><span class="text-danger">你好，欢迎光临我的博客！</span></p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">社区</h3>
                    </div>
                    <div class="panel-body">
                        <p><a href="https://www.fjsmu.edu.cn" target="_blank" rel="noopener noreferrer" class="text-danger">三明学院</a></p>
                        <p><a href="http://www.leelom.com" target="_blank" rel="noopener noreferrer" class="text-danger">里弄社区</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="container">
        <hr>
        <p class="text-center">Copyright © leelom.com 版权所有 | 京ICP备08102442号</p>
    </footer>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                    <h4 class="modal-title">登录</h4>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="loginUsername">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" name="username" placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label for="loginPassword">密码</label>
                            <input type="password" class="form-control" id="loginPassword" name="password" placeholder="请输入密码">
                        </div>
                        <p class="text-danger" id="loginError"></p>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="loginSubmit">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                    <h4 class="modal-title">注册</h4>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="form-group">
                            <label for="registerUsername">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" name="username" placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label for="registerEmail">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" name="email" placeholder="请输入邮箱">
                        </div>
                        <div class="form-group">
                            <label for="registerPassword">密码</label>
                            <input type="password" class="form-control" id="registerPassword" name="password" placeholder="请输入密码">
                        </div>
                        <div class="form-group">
                            <label for="registerRepassword">确认密码</label>
                            <input type="password" class="form-control" id="registerRepassword" name="repassword" placeholder="请确认密码">
                        </div>
                        <p class="text-danger" id="registerError"></p>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="registerSubmit">注册</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkAdminAccess() {
            // 清除普通用户的cookie信息
            $.ajax({
                url: '/api/user/logout',
                success: function() {
                    // 检查localStorage中是否有管理员令牌
                    const adminToken = localStorage.getItem('adminToken');
                    
                    if (!adminToken) {
                        // 如果没有令牌，直接跳转到管理员登录页面
                        window.location.href = '/admin/login';
                    } else {
                        // 如果有令牌，先验证令牌是否有效
                        $.ajax({
                            type: 'GET',
                            url: '/api/admin/check-auth',
                            headers: {
                                'Authorization': 'Bearer ' + adminToken
                            },
                            success: function(response) {
                                // 令牌有效，跳转到管理后台
                                window.location.href = '/admin';
                            },
                            error: function() {
                                // 令牌无效，跳转到登录页面
                                localStorage.removeItem('adminToken');
                                window.location.href = '/admin/login';
                            }
                        });
                    }
                }
            });
        }
    </script>

    {% block script %}{% endblock %}
</body>
</html>