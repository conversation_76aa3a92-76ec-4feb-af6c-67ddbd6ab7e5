{% extends 'layout.html' %}

{% block main %}
<div class="panel panel-default">
    <div class="panel-heading">
        <h1 class="panel-title">{{content.title}}</h1>
        <p class="text-muted" style="margin-top: 10px;">
            作者：<span>{{content.user.username}}</span> |
            时间：<span>{{content.addTime|date('Y年m月d日 H:i:s', -8)}}</span> |
            阅读：<span>{{content.views}}</span> |
            评论：<span>{{content.comments.length}}</span>
        </p>
    </div>
    <div class="panel-body">
        {{content.content}}
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">评论</h3>
    </div>
    <div class="panel-body">
        {% if userInfo._id %}
        <form id="commentForm">
            <div class="form-group">
                <textarea name="content" class="form-control" rows="3" placeholder="请输入评论内容"></textarea>
            </div>
            <button type="submit" class="btn btn-primary">提交评论</button>
        </form>
        {% else %}
        <p class="text-muted">请先登录后评论</p>
        {% endif %}

        <hr>

        {% if comments.length > 0 %}
        <ul class="media-list">
            {% for comment in comments %}
            <li class="media">
                <div class="media-body">
                    <h4 class="media-heading">{{comment.username}}
                        <small class="pull-right">{{comment.postTime|date('Y年m月d日 H:i:s', -8)}}</small>
                    </h4>
                    <p>{{comment.content}}</p>
                </div>
            </li>
            {% if not loop.last %}
            <hr>
            {% endif %}
            {% endfor %}
        </ul>

        <nav aria-label="Page navigation" class="text-center">
            <ul class="pagination">
                {% if page <= 1 %}
                <li class="disabled">
                    <span aria-hidden="true">&laquo;</span>
                </li>
                {% else %}
                <li>
                    <a href="/view?contentid={{content.id}}&page={{page-1}}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}

                {% for i in range(1, pages + 1) %}
                <li {% if page == i %}class="active"{% endif %}>
                    <a href="/view?contentid={{content.id}}&page={{i}}">{{i}}</a>
                </li>
                {% endfor %}

                {% if page >= pages %}
                <li class="disabled">
                    <span aria-hidden="true">&raquo;</span>
                </li>
                {% else %}
                <li>
                    <a href="/view?contentid={{content.id}}&page={{page+1}}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% else %}
        <p class="text-muted">暂时还没有评论</p>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block script %}
<script>
$(function() {
    $('#commentForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            type: 'POST',
            url: '/api/comment/post',
            data: {
                contentid: '{{content.id}}',
                content: $(this).find('[name=content]').val()
            },
            success: function(result) {
                if (result.code == 0) {
                    window.location.reload();
                }
            }
        });
    });
});
</script>
{% endblock %}