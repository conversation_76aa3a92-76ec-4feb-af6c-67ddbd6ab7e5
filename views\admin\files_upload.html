{% extends 'layout.html' %}

{% block main %}
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><a href="/admin">管理首页</a></li>
        <li class="active">文件上传</li>
    </ol>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">单文件上传</h3>
        </div>
        <div class="panel-body">
            <form id="singleUploadForm" class="form-horizontal" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="singleFile" class="col-sm-2 control-label">选择文件</label>
                    <div class="col-sm-6">
                        <input type="file" id="singleFile" name="file" class="form-control" accept="image/*" aria-label="选择单个图片文件" title="选择要上传的图片">
                    </div>
                    <div class="col-sm-4">
                        <button type="submit" class="btn btn-primary">上传</button>
                    </div>
                </div>
            </form>
            <div id="singlePreview" class="row" style="margin-top: 20px;"></div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">多文件上传</h3>
        </div>
        <div class="panel-body">
            <form id="multipleUploadForm" class="form-horizontal" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="multipleFiles" class="col-sm-2 control-label">选择文件</label>
                    <div class="col-sm-6">
                        <input type="file" id="multipleFiles" name="files" class="form-control" accept="image/*" multiple aria-label="选择多个图片文件" title="选择要上传的多个图片">
                    </div>
                    <div class="col-sm-4">
                        <button type="submit" class="btn btn-primary">上传</button>
                    </div>
                </div>
            </form>
            <div id="multiplePreview" class="row" style="margin-top: 20px;"></div>
        </div>
    </div>
</div>

<script>
document.getElementById('singleUploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/upload/single', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const previewHtml = `
                <div class="col-sm-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">${data.data.originalName}</h3>
                        </div>
                        <div class="panel-body">
                            <div class="thumbnail">
                                <img src="${data.data.processedPath}" alt="处理后的图片">
                            </div>
                            <div class="thumbnail">
                                <img src="${data.data.thumbnailPath}" alt="缩略图">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('singlePreview').innerHTML = previewHtml;
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('上传失败，请重试');
    });
});

document.getElementById('multipleUploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/upload/multiple', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const previewHtml = data.data.map(file => `
                <div class="col-sm-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">${file.originalName}</h3>
                        </div>
                        <div class="panel-body">
                            <div class="thumbnail">
                                <img src="${file.processedPath}" alt="处理后的图片">
                            </div>
                            <div class="thumbnail">
                                <img src="${file.thumbnailPath}" alt="缩略图">
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
            document.getElementById('multiplePreview').innerHTML = previewHtml;
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('上传失败，请重试');
    });
});
</script>
{% endblock %}