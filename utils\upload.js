const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = 'uploads/';
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// File filter function
const fileFilter = (req, file, cb) => {
    // Accept images only
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
        return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
};

// Create multer upload instance
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    }
});

// Image processing function
const processImage = async (filePath, options = {}) => {
    const {
        width = 800,
        height = 600,
        quality = 80,
        format = 'jpeg'
    } = options;

    const processedPath = filePath.replace(/\.\w+$/, '_processed.' + format);

    try {
        await sharp(filePath)
            .resize(width, height, {
                fit: 'inside',
                withoutEnlargement: true
            })
            .toFormat(format, { quality })
            .toFile(processedPath);

        return {
            originalPath: filePath,
            processedPath: processedPath
        };
    } catch (error) {
        throw new Error('Error processing image: ' + error.message);
    }
};

// Thumbnail generation function
const generateThumbnail = async (filePath) => {
    const thumbnailPath = filePath.replace(/\.\w+$/, '_thumb.jpg');

    try {
        await sharp(filePath)
            .resize(200, 200, {
                fit: 'cover',
                position: 'center'
            })
            .toFormat('jpeg', { quality: 70 })
            .toFile(thumbnailPath);

        return thumbnailPath;
    } catch (error) {
        throw new Error('Error generating thumbnail: ' + error.message);
    }
};

module.exports = {
    upload,
    processImage,
    generateThumbnail
}; 