{% extends 'layout.html' %}

{% block main %}
<div class="reset-container">
    <h3>重置密码</h3>
    <div class="reset-form">
        <div class="alert alert-danger" id="reset-error" style="display: none;"></div>
        <div class="alert alert-success" id="reset-success" style="display: none;"></div>
        
        <form id="resetForm">
            <input type="hidden" id="email" name="email" value="{{ email }}">
            <input type="hidden" id="token" name="token" value="{{ token }}">
            
            <div class="form-group">
                <label for="password">新密码</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="repassword">确认新密码</label>
                <input type="password" class="form-control" id="repassword" name="repassword" required>
            </div>
            
            <button type="submit" class="btn btn-primary">重置密码</button>
        </form>
    </div>
</div>
{% endblock %}

{% block head %}
<style>
    .reset-container {
        max-width: 500px;
        margin: 0 auto;
        padding: 20px;
    }
    .reset-form {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    .form-group {
        margin-bottom: 20px;
    }
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
    }
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-danger {
        color: #a94442;
        background-color: #f2dede;
        border-color: #ebccd1;
    }
    .alert-success {
        color: #3c763d;
        background-color: #dff0d8;
        border-color: #d6e9c6;
    }
</style>
{% endblock %}

{% block script %}
<script>
    $(function() {
        $('#resetForm').on('submit', function(e) {
            e.preventDefault();
            
            // 隐藏之前的消息
            $('#reset-error').hide();
            $('#reset-success').hide();
            
            // 获取表单数据
            var formData = {
                email: $('#email').val(),
                token: $('#token').val(),
                password: $('#password').val(),
                repassword: $('#repassword').val()
            };
            
            // 验证密码
            if (formData.password !== formData.repassword) {
                $('#reset-error').text('两次输入的密码不一致').show();
                return;
            }
            
            // 发送AJAX请求
            $.ajax({
                type: 'POST',
                url: '/api/user/reset-password',
                data: formData,
                success: function(result) {
                    if (result.code === 0) {
                        // 成功
                        $('#reset-success').text(result.message).show();
                        $('#resetForm')[0].reset();
                        
                        // 3秒后跳转到登录页
                        setTimeout(function() {
                            window.location.href = '/';
                        }, 3000);
                    } else {
                        // 错误
                        $('#reset-error').text(result.message).show();
                    }
                },
                error: function() {
                    $('#reset-error').text('重置密码失败，请稍后重试').show();
                }
            });
        });
    });
</script>
{% endblock %} 